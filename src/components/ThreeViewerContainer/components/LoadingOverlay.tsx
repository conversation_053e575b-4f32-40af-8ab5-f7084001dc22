import React from 'react';

interface LoadingOverlayProps {
  isLoading: boolean;
  error: string | null;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  error,
}) => {
  if (!isLoading && !error) return null;

  return (
    <>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/30">
          <div className="bg-white p-5 rounded-lg shadow-lg border border-gray-300">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-800 font-medium">
                Loading model...
              </span>
            </div>
          </div>
        </div>
      )}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/30">
          <div className="bg-white p-5 rounded-lg shadow-lg border border-red-200 max-w-md">
            <div className="flex items-start gap-3">
              <div className="text-red-600 text-xl">⚠️</div>
              <div>
                <h3 className="font-bold text-red-600 mb-1">
                  Error Loading Model
                </h3>
                <p className="text-gray-800">{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default LoadingOverlay;
