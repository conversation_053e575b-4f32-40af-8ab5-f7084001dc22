import React from 'react';
import { TransformMode } from '../utils/objectTransformations';

interface HelpPanelProps {
  transformMode: TransformMode;
  onClose: () => void;
}

const HelpPanel: React.FC<HelpPanelProps> = ({ transformMode, onClose }) => {
  return (
    <div className="absolute top-4 right-4 bg-white p-4 rounded shadow max-w-xs border border-gray-300 text-gray-800">
      <h3 className="font-bold mb-2 text-black text-lg">Controls</h3>
      <ul className="text-sm space-y-1">
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            Click
          </span>{' '}
          - Select an object
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            Shift+Click
          </span>{' '}
          - Add/remove from selection
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            Ctrl+A
          </span>{' '}
          - Select all objects
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            Esc
          </span>{' '}
          - Clear selection
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            T
          </span>{' '}
          - Translation mode
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            R
          </span>{' '}
          - Rotation mode
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            S
          </span>{' '}
          - Scale mode
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            Arrow keys
          </span>{' '}
          - Transform X/Y
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            Page Up/Down
          </span>{' '}
          - Transform Z
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            +/-
          </span>{' '}
          - Adjust transform speed
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            H
          </span>{' '}
          - Toggle this help
        </li>
      </ul>

      <h3 className="font-bold mt-3 mb-1 text-black">History</h3>
      <ul className="text-sm space-y-1">
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            Undo
          </span>{' '}
          - Revert to previous state (Ctrl+Z)
        </li>
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            Redo
          </span>{' '}
          - Restore undone changes (Ctrl+Y)
        </li>
      </ul>

      <h3 className="font-bold mt-3 mb-1 text-black">AI Assistant</h3>
      <ul className="text-sm space-y-1">
        <li>
          <span className="font-mono font-semibold bg-gray-100 px-1 rounded">
            AI Panel
          </span>{' '}
          - Toggle the AI prompt panel
        </li>
        <li>Use natural language to describe scene changes</li>
        <li>Example: &quot;Place table in the left corner&quot;</li>
      </ul>

      <h3 className="font-bold mt-3 mb-1 text-black">Model Placement</h3>
      <ul className="text-sm space-y-1">
        <li>Models are automatically positioned on top of the grid</li>
      </ul>
      <div className="mt-2 text-sm">
        Current mode:{' '}
        <span className="font-bold text-black">{transformMode}</span>
      </div>
      <button
        className="mt-3 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
        onClick={onClose}
      >
        Close
      </button>
    </div>
  );
};

export default HelpPanel;
