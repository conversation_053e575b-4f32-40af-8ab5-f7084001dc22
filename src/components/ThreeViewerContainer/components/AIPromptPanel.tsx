import React, { useState, useRef, useEffect } from 'react';

interface AIPromptPanelProps {
  onSubmitPrompt: (prompt: string) => void;
  promptHistory: {
    prompt: string;
    response: string;
    timestamp: number;
  }[];
  isProcessing: boolean;
  modelAnalyzed?: boolean;
}

const AIPromptPanel: React.FC<AIPromptPanelProps> = ({
  onSubmitPrompt,
  promptHistory,
  isProcessing,
  modelAnalyzed = true, // Default to true for backward compatibility
}) => {
  const [prompt, setPrompt] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const historyRef = useRef<HTMLDivElement>(null);

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // Scroll to bottom of history when new items are added
  useEffect(() => {
    if (historyRef.current) {
      historyRef.current.scrollTop = historyRef.current.scrollHeight;
    }
  }, [promptHistory]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (prompt.trim() && !isProcessing) {
      onSubmitPrompt(prompt);
      setPrompt('');
    }
  };

  // Format timestamp to readable time
  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="absolute top-0 right-0 w-80 h-full bg-white shadow-lg border-l border-gray-300 flex flex-col">
      <div className="p-3 bg-blue-600 text-white font-bold border-b border-blue-700">
        <h3 className="text-lg">AI Scene Assistant</h3>
      </div>

      {/* Prompt history */}
      <div
        ref={historyRef}
        className="flex-1 overflow-y-auto p-3 space-y-4 bg-gray-50"
      >
        {!modelAnalyzed ? (
          <div className="text-yellow-600 italic text-center mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <p className="font-bold">Model not analyzed yet</p>
            <p className="mt-2">
              Please click the &quot;Analyze Model&quot; button first to enable
              AI assistance.
            </p>
          </div>
        ) : promptHistory.length === 0 ? (
          <div className="text-gray-500 italic text-center mt-8">
            <p>No prompts yet. Try asking the AI to modify your scene.</p>
            <p className="mt-2 text-sm">
              Example: &quot;Place the table in the corner&quot;
            </p>
          </div>
        ) : (
          promptHistory.map((item, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-start">
                <div className="bg-blue-100 rounded-lg p-2 text-blue-800 flex-1">
                  <div className="font-medium">{item.prompt}</div>
                  <div className="text-xs text-blue-600 mt-1">
                    {formatTime(item.timestamp)}
                  </div>
                </div>
              </div>
              <div className="flex items-start">
                <div className="bg-gray-100 rounded-lg p-2 text-gray-800 flex-1">
                  <div className="whitespace-pre-line text-sm">
                    {item.response}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}

        {isProcessing && (
          <div className="flex items-center justify-center space-x-2 text-gray-500 mt-4">
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-150"></div>
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-300"></div>
            <span className="ml-2">Processing...</span>
          </div>
        )}
      </div>

      {/* Prompt input */}
      <div className="p-3 border-t border-gray-300 bg-white">
        <form onSubmit={handleSubmit} className="flex items-center">
          <input
            ref={inputRef}
            type="text"
            value={prompt}
            onChange={e => setPrompt(e.target.value)}
            placeholder={
              modelAnalyzed
                ? 'Describe scene changes...'
                : 'Analyze model first...'
            }
            className="flex-1 p-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
            disabled={isProcessing || !modelAnalyzed}
          />
          <button
            type="submit"
            className={`p-2 rounded-r ${
              isProcessing || !prompt.trim() || !modelAnalyzed
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            } text-white`}
            disabled={isProcessing || !prompt.trim() || !modelAnalyzed}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </form>
        <div className="mt-2 text-xs text-gray-500">
          Press Enter to submit your prompt to Claude Opus AI
        </div>
      </div>
    </div>
  );
};

export default AIPromptPanel;
