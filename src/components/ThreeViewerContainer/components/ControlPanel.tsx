import React from 'react';
import * as THREE from 'three';
import { TransformMode } from '../utils/objectTransformations';
import { AIServiceType } from '../utils/aiService';

interface ControlPanelProps {
  selectedObjects: THREE.Object3D[];
  transformMode: TransformMode;
  transformSpeed: number;
  toggleShowHelp: () => void;
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  showAIPanel: boolean;
  onToggleAIPanel: () => void;
  onClearSelection?: () => void;
  modelLoaded: boolean;
  modelAnalyzed: boolean;
  isAnalyzing: boolean;
  onAnalyzeModel: () => void;
  onAnalyzeModelBasic?: () => void; // Added for basic analysis fallback
  analysisError?: boolean; // Added to show fallback button
  aiServiceType: AIServiceType;
  onChangeAIService: (serviceType: AIServiceType) => void;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
  selectedObjects,
  transformMode,
  transformSpeed,
  toggleShowHelp,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  showAIPanel,
  onToggleAIPanel,
  onClearSelection,
  modelLoaded,
  modelAnalyzed,
  isAnalyzing,
  onAnalyzeModel,
  onAnalyzeModelBasic,
  analysisError = false,
  aiServiceType,
  onChangeAIService,
}) => {
  return (
    <div className="absolute bottom-4 left-4 bg-white p-3 rounded shadow text-sm border border-gray-300 text-gray-800">
      <div className="flex flex-col gap-2">
        <div className="flex gap-2">
          <button
            className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 font-medium flex-1"
            onClick={toggleShowHelp}
          >
            Show Controls (H)
          </button>

          {modelLoaded && !modelAnalyzed && (
            <div className="flex flex-col gap-1 flex-1">
              <button
                className={`px-3 py-1 rounded font-medium ${
                  isAnalyzing
                    ? 'bg-yellow-400 text-yellow-900 cursor-wait'
                    : analysisError
                      ? 'bg-red-500 text-white hover:bg-red-600'
                      : 'bg-yellow-500 text-white hover:bg-yellow-600'
                }`}
                onClick={onAnalyzeModel}
                disabled={isAnalyzing}
              >
                {isAnalyzing
                  ? 'Analyzing...'
                  : analysisError
                    ? 'Retry Analysis'
                    : 'Analyze Model'}
              </button>

              {analysisError && onAnalyzeModelBasic && (
                <button
                  className="px-3 py-1 rounded font-medium bg-gray-500 text-white hover:bg-gray-600"
                  onClick={onAnalyzeModelBasic}
                  disabled={isAnalyzing}
                  title="Use basic analysis without AI"
                >
                  Basic Analysis
                </button>
              )}
            </div>
          )}

          <div className="flex flex-col gap-1 flex-1">
            <button
              className={`px-3 py-1 rounded font-medium ${
                showAIPanel
                  ? 'bg-purple-600 text-white hover:bg-purple-700'
                  : 'bg-purple-100 text-purple-800 hover:bg-purple-200'
              }`}
              onClick={onToggleAIPanel}
              disabled={!modelAnalyzed}
              title={
                !modelAnalyzed
                  ? 'Analyze model first to enable AI assistant'
                  : 'Open AI assistant'
              }
            >
              AI Assistant
            </button>

            <div className="flex text-xs gap-1 mt-1">
              <button
                className={`px-2 py-0.5 rounded flex-1 ${
                  aiServiceType === 'claude'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                onClick={() => onChangeAIService('claude')}
                title="Use Claude AI"
              >
                Claude
              </button>
              <button
                className={`px-2 py-0.5 rounded flex-1 ${
                  aiServiceType === 'gemini'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                onClick={() => onChangeAIService('gemini')}
                title="Use Google Gemini AI"
              >
                Gemini
              </button>
            </div>
          </div>
        </div>

        {/* Undo/Redo buttons */}
        <div className="flex gap-2">
          <button
            className={`flex-1 px-3 py-1 rounded font-medium ${
              canUndo
                ? 'bg-gray-700 text-white hover:bg-gray-800'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
            onClick={onUndo}
            disabled={!canUndo}
            title="Undo (Ctrl+Z)"
          >
            Undo
          </button>

          <button
            className={`flex-1 px-3 py-1 rounded font-medium ${
              canRedo
                ? 'bg-gray-700 text-white hover:bg-gray-800'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
            onClick={onRedo}
            disabled={!canRedo}
            title="Redo (Ctrl+Y)"
          >
            Redo
          </button>
        </div>

        <div className="bg-gray-100 p-2 rounded text-xs text-gray-600">
          Models are automatically placed on the grid
        </div>

        {selectedObjects.length > 0 && (
          <div className="mt-1 bg-gray-100 p-2 rounded">
            <div className="flex justify-between items-center mb-1">
              <div className="font-bold text-black">
                {selectedObjects.length === 1
                  ? '1 object selected'
                  : `${selectedObjects.length} objects selected`}
              </div>
              {selectedObjects.length > 0 && onClearSelection && (
                <button
                  onClick={onClearSelection}
                  className="text-xs bg-gray-300 hover:bg-gray-400 text-gray-800 px-2 py-0.5 rounded"
                  title="Clear selection (Esc)"
                >
                  Clear
                </button>
              )}
            </div>
            <div>
              Mode:{' '}
              <span className="font-bold text-black">{transformMode}</span>
            </div>
            <div>
              Speed:{' '}
              <span className="font-bold text-black">
                {transformSpeed.toFixed(2)}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ControlPanel;
