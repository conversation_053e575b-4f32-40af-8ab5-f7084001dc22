// 'use client';

// import { useEffect, useCallback, useState } from 'react';
// import * as THREE from 'three';
// import { EnhancedAIService } from '@/lib/enhancedAIService';
// import { useModelDatabase } from '@/hooks/useModelDatabase';
// import type { SceneSetup } from './utils/sceneSetup';
// import type { AIServiceType } from './utils/aiService';

// interface DatabaseIntegrationProps {
//   sceneSetupRef: React.RefObject<SceneSetup | null>;
//   modelRef: React.RefObject<THREE.Object3D | null>;
//   aiServiceType: AIServiceType;
//   currentFileName: string | null;
//   onAnalysisComplete?: (analysisId: string) => void;
//   onPromptComplete?: (promptId: string) => void;
//   onStateLoaded?: () => void;
// }

// export default function DatabaseIntegration({
//   sceneSetupRef,
//   aiServiceType,
//   currentFileName,
//   onAnalysisComplete,
//   onPromptComplete,
//   onStateLoaded,
// }: DatabaseIntegrationProps) {
//   const [enhancedAIService, setEnhancedAIService] =
//     useState<EnhancedAIService | null>(null);
//   const [isInitialized, setIsInitialized] = useState(false);

//   const {
//     loading: dbLoading,
//     error: dbError,
//     createOrGetModel,
//   } = useModelDatabase();

//   // Инициализация EnhancedAIService
//   useEffect(() => {
//     const service = new EnhancedAIService(aiServiceType);
//     setEnhancedAIService(service);
//     setIsInitialized(false);
//   }, [aiServiceType]);

//   // Инициализация модели в базе данных при загрузке файла
//   useEffect(() => {
//     const initializeModel = async () => {
//       if (!enhancedAIService || !currentFileName || !sceneSetupRef.current) {
//         return;
//       }

//       try {
//         console.log(`Initializing model in database: ${currentFileName}`);

//         // Установить текущую модель в EnhancedAIService
//         await enhancedAIService.setCurrentModel(currentFileName);

//         // Создать или получить модель в базе данных
//         await createOrGetModel(currentFileName);

//         // Попытаться загрузить сохраненное состояние модели
//         await loadSavedModelState();

//         setIsInitialized(true);
//         console.log(`Model initialized: ${currentFileName}`);
//       } catch (error) {
//         console.error('Error initializing model:', error);
//       }
//     };

//     initializeModel();
//   }, [enhancedAIService, currentFileName, sceneSetupRef, createOrGetModel]);

//   // Загрузка сохраненного состояния модели
//   const loadSavedModelState = useCallback(async () => {
//     if (!enhancedAIService || !currentFileName || !sceneSetupRef.current) {
//       return;
//     }

//     try {
//       console.log('Loading saved model state...');
//       await enhancedAIService.loadModelState(sceneSetupRef.current.scene);
//       onStateLoaded?.();
//       console.log('Model state loaded successfully');
//     } catch (error) {
//       console.error('Error loading model state:', error);
//     }
//   }, [enhancedAIService, currentFileName, sceneSetupRef, onStateLoaded]);

//   // Анализ сцены с сохранением в базу данных
//   const analyzeSceneWithDatabase = useCallback(
//     async (useFallback: boolean = false) => {
//       if (!enhancedAIService || !sceneSetupRef.current || !isInitialized) {
//         throw new Error('Service not initialized');
//       }

//       try {
//         console.log('Starting scene analysis with database...');
//         const analysisId = await enhancedAIService.analyzeSceneWithDatabase(
//           sceneSetupRef.current.scene,
//           useFallback,
//         );

//         if (analysisId) {
//           onAnalysisComplete?.(analysisId);
//           console.log(`Scene analysis completed with ID: ${analysisId}`);
//         }

//         return analysisId;
//       } catch (error) {
//         console.error('Error analyzing scene:', error);
//         throw error;
//       }
//     },
//     [enhancedAIService, sceneSetupRef, isInitialized, onAnalysisComplete],
//   );

//   // Обработка промпта с сохранением в базу данных
//   const processPromptWithDatabase = useCallback(
//     async (prompt: string, promptType: string = 'modification') => {
//       if (!enhancedAIService || !sceneSetupRef.current || !isInitialized) {
//         throw new Error('Service not initialized');
//       }

//       try {
//         console.log('Processing prompt with database...');
//         const response = await enhancedAIService.processPromptWithDatabase(
//           prompt,
//           sceneSetupRef.current.scene,
//           promptType,
//         );

//         onPromptComplete?.(response.promptId);
//         console.log(`Prompt processed with ID: ${response.promptId}`);

//         return response;
//       } catch (error) {
//         console.error('Error processing prompt:', error);
//         throw error;
//       }
//     },
//     [enhancedAIService, sceneSetupRef, isInitialized, onPromptComplete],
//   );

//   // Сохранение текущего состояния модели
//   const saveCurrentState = useCallback(async () => {
//     if (!enhancedAIService || !sceneSetupRef.current || !isInitialized) {
//       return;
//     }

//     try {
//       console.log('Saving current model state...');
//       await enhancedAIService.saveCurrentModelState(
//         sceneSetupRef.current.scene,
//       );
//       console.log('Model state saved successfully');
//     } catch (error) {
//       console.error('Error saving model state:', error);
//     }
//   }, [enhancedAIService, sceneSetupRef, isInitialized]);

//   // Получение истории промптов
//   const getPromptHistory = useCallback(
//     async (limit: number = 10) => {
//       if (!enhancedAIService || !isInitialized) {
//         return [];
//       }

//       try {
//         return await enhancedAIService.getPromptHistory(limit);
//       } catch (error) {
//         console.error('Error getting prompt history:', error);
//         return [];
//       }
//     },
//     [enhancedAIService, isInitialized],
//   );

//   // Получение последнего анализа
//   const getLatestAnalysis = useCallback(async () => {
//     if (!enhancedAIService || !isInitialized) {
//       return null;
//     }

//     try {
//       return await enhancedAIService.getModelAnalysis();
//     } catch (error) {
//       console.error('Error getting latest analysis:', error);
//       return null;
//     }
//   }, [enhancedAIService, isInitialized]);

//   // Получение информации о текущей модели
//   const getCurrentModelInfo = useCallback(async () => {
//     if (!enhancedAIService || !isInitialized) {
//       return null;
//     }

//     try {
//       return await enhancedAIService.getCurrentModelInfo();
//     } catch (error) {
//       console.error('Error getting current model info:', error);
//       return null;
//     }
//   }, [enhancedAIService, isInitialized]);

//   // Автосохранение состояния при изменениях в сцене
//   useEffect(() => {
//     if (!isInitialized || !sceneSetupRef.current) {
//       return;
//     }

//     // Создать интервал для автосохранения каждые 30 секунд
//     const autoSaveInterval = setInterval(() => {
//       saveCurrentState();
//     }, 30000);

//     return () => {
//       clearInterval(autoSaveInterval);
//     };
//   }, [isInitialized, saveCurrentState]);

//   // Экспорт функций для использования в родительском компоненте
//   return {
//     isInitialized,
//     dbLoading,
//     dbError,
//     enhancedAIService,
//     analyzeSceneWithDatabase,
//     processPromptWithDatabase,
//     saveCurrentState,
//     loadSavedModelState,
//     getPromptHistory,
//     getLatestAnalysis,
//     getCurrentModelInfo,
//   };
// }
