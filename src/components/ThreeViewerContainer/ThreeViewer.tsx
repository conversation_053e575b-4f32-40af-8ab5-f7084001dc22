'use client';

import { useEffect, useRef, useCallback } from 'react';
import * as THREE from 'three';

// Import utilities
import { setupScene, SceneSetup } from './utils/sceneSetup';
import { loadModel, processModel } from './utils/modelLoaders';
import {
  TransformMode,
  transformObject,
  createSelectionOutline,
} from './utils/objectTransformations';
import HistoryManager from './utils/historyManager';

interface ThreeViewerProps {
  modelData: { url: string; fileType: string } | null;
  setIsLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setSelectedObjects: (objects: THREE.Object3D[]) => void;
  selectedObjects: THREE.Object3D[];
  transformMode: TransformMode;
  setTransformMode: (mode: TransformMode) => void;
  transformSpeed: number;
  setTransformSpeed: (speed: number) => void;
  sceneSetupRef: React.RefObject<SceneSetup | null>;
  modelRef: React.RefObject<THREE.Object3D | null>;
  historyManagerRef: React.RefObject<HistoryManager>;
  setCanUndo: (canUndo: boolean) => void;
  setCanRedo: (canRedo: boolean) => void;
}

export default function ThreeViewer({
  modelData,
  setIsLoading,
  setError,
  setSelectedObjects,
  selectedObjects,
  transformMode,
  setTransformMode,
  transformSpeed,
  setTransformSpeed,
  sceneSetupRef,
  modelRef,
  historyManagerRef,
  setCanUndo,
  setCanRedo,
}: ThreeViewerProps) {
  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const outlinesRef = useRef<Map<string, THREE.LineSegments>>(new Map());
  const raycasterRef = useRef<THREE.Raycaster>(new THREE.Raycaster());
  const mouseDownPositionRef = useRef<{ x: number; y: number } | null>(null);
  const isDraggingRef = useRef<boolean>(false);

  // Function to update selection outlines for multiple objects
  const updateSelectionOutlines = useCallback(() => {
    if (!sceneSetupRef.current) return;

    // Remove all existing outlines
    outlinesRef.current.forEach(outline => {
      sceneSetupRef.current?.scene.remove(outline);
    });
    outlinesRef.current.clear();

    // Create new outlines for all selected objects
    selectedObjects.forEach(object => {
      const outline = createSelectionOutline(object);
      sceneSetupRef.current?.scene.add(outline);
      outlinesRef.current.set(object.uuid, outline);
    });
  }, [selectedObjects, sceneSetupRef]);

  // Function to clear all selection outlines
  const clearSelectionOutlines = useCallback(() => {
    if (!sceneSetupRef.current) return;

    // Remove all existing outlines
    outlinesRef.current.forEach(outline => {
      sceneSetupRef.current?.scene.remove(outline);
    });
    outlinesRef.current.clear();
  }, [sceneSetupRef]);

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    // Setup scene
    const sceneSetup = setupScene(containerRef.current);
    sceneSetupRef.current = sceneSetup;

    // Cleanup function
    return () => {
      sceneSetup.cleanup();
      sceneSetupRef.current = null;
    };
  }, [containerRef, sceneSetupRef]);

  // Load 3D model when modelData changes
  useEffect(() => {
    if (!sceneSetupRef.current) {
      console.log('Model loading effect skipped: sceneSetupRef is null');
      return;
    }

    // If no model data, we're done (model was cleared)
    if (!modelData) {
      console.log('No model data, skipping model load');
      return;
    }

    // Clear any selected objects and outlines
    setSelectedObjects([]);
    clearSelectionOutlines();

    // Remove previous model if exists
    if (modelRef.current && sceneSetupRef.current.scene) {
      console.log('Removing previous model');
      sceneSetupRef.current.scene.remove(modelRef.current);
      modelRef.current = null;
    }

    setIsLoading(true);
    setError(null);

    const { url, fileType } = modelData;

    const loadModelAsync = async () => {
      try {
        // Load the model
        const object = await loadModel(url, fileType);

        if (!object) {
          throw new Error('Failed to load model: Object is null');
        }

        if (sceneSetupRef.current) {
          console.log('Model loaded successfully:', object);

          // Process the model (center, scale, etc.) - always place on grid
          const box = processModel(object);

          // Reset camera position based on model size
          if (box.isEmpty()) {
            sceneSetupRef.current.camera.position.set(5, 5, 5);
          } else {
            sceneSetupRef.current.camera.position.set(10, 5, 10);
          }

          sceneSetupRef.current.camera.updateProjectionMatrix();
          sceneSetupRef.current.controls.target.set(0, 0, 0);
          sceneSetupRef.current.controls.update();

          // Add the model to the scene
          sceneSetupRef.current.scene.add(object);
          modelRef.current = object;

          console.log('Model added to scene');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load model');
      } finally {
        setIsLoading(false);
      }
    };

    // Only reload the model if modelData changes
    loadModelAsync();
  }, [
    modelData,
    clearSelectionOutlines,
    modelRef,
    sceneSetupRef,
    setError,
    setIsLoading,
    setSelectedObjects,
  ]);

  // Handle object selection
  const handleObjectSelection = useCallback(
    (event: MouseEvent) => {
      if (!containerRef.current || !sceneSetupRef.current || !modelRef.current)
        return;

      // Check if shift key is pressed for multi-selection
      const isMultiSelect = event.shiftKey;

      // Calculate mouse position in normalized device coordinates
      const rect = containerRef.current.getBoundingClientRect();
      const x =
        ((event.clientX - rect.left) / containerRef.current.clientWidth) * 2 -
        1;
      const y =
        -((event.clientY - rect.top) / containerRef.current.clientHeight) * 2 +
        1;

      // Update the raycaster
      const mousePosition = new THREE.Vector2(x, y);
      raycasterRef.current.setFromCamera(
        mousePosition,
        sceneSetupRef.current.camera,
      );

      // Find all intersected objects
      const selectableMeshes: THREE.Object3D[] = [];
      modelRef.current.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          selectableMeshes.push(child);
        }
      });

      const intersects =
        raycasterRef.current.intersectObjects(selectableMeshes);

      if (intersects.length > 0) {
        const clickedObject = intersects[0].object;

        if (isMultiSelect) {
          // If shift key is pressed, toggle selection of the clicked object
          const objectIndex = selectedObjects.findIndex(
            obj => obj.uuid === clickedObject.uuid,
          );

          if (objectIndex >= 0) {
            // Object is already selected, remove it from selection
            const newSelectedObjects = [...selectedObjects];
            newSelectedObjects.splice(objectIndex, 1);
            setSelectedObjects(newSelectedObjects);
          } else {
            // Object is not selected, add it to selection
            setSelectedObjects([...selectedObjects, clickedObject]);
          }
        } else {
          // If shift key is not pressed, select only the clicked object
          setSelectedObjects([clickedObject]);
        }

        // Disable orbit controls temporarily to avoid conflicts
        if (sceneSetupRef.current) {
          sceneSetupRef.current.controls.enabled = false;
          // Re-enable after a short delay
          setTimeout(() => {
            if (sceneSetupRef.current) {
              sceneSetupRef.current.controls.enabled = true;
            }
          }, 100);
        }
      } else if (!isMultiSelect) {
        // Deselect all if clicking on empty space and not multi-selecting
        setSelectedObjects([]);
      }
    },
    [
      selectedObjects,
      containerRef,
      modelRef,
      sceneSetupRef,
      setSelectedObjects,
    ],
  );

  // Handle keyboard controls for object transformation
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Toggle help screen with 'H' key
      if (event.key === 'h' || event.key === 'H') {
        return;
      }

      // Toggle transform mode with 'T', 'R', 'S' keys
      if (event.key === 't' || event.key === 'T') {
        setTransformMode('translate');
        return;
      }
      if (event.key === 'r' || event.key === 'R') {
        setTransformMode('rotate');
        return;
      }
      if (event.key === 's' || event.key === 'S') {
        setTransformMode('scale');
        return;
      }

      // Adjust transform speed with '+' and '-' keys
      if (event.key === '+' || event.key === '=') {
        setTransformSpeed(Math.min(transformSpeed + 0.05, 1));
        return;
      }
      if (event.key === '-' || event.key === '_') {
        setTransformSpeed(Math.max(transformSpeed - 0.05, 0.01));
        return;
      }

      // Clear selection with Escape key
      if (event.key === 'Escape') {
        setSelectedObjects([]);
        return;
      }

      // Select all objects with Ctrl+A
      if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {
        event.preventDefault(); // Prevent browser's select all
        if (modelRef.current) {
          const allSelectableObjects: THREE.Object3D[] = [];
          modelRef.current.traverse((child: THREE.Object3D) => {
            if (child instanceof THREE.Mesh) {
              allSelectableObjects.push(child);
            }
          });
          setSelectedObjects(allSelectableObjects);
        }
        return;
      }

      // Only apply transformations if there are selected objects
      if (selectedObjects.length > 0) {
        // Apply transformations to all selected objects
        selectedObjects.forEach(object => {
          transformObject(object, event.key, transformMode, transformSpeed);
        });

        // Save state after transformation
        if (
          sceneSetupRef.current &&
          (event.key === 'ArrowUp' ||
            event.key === 'ArrowDown' ||
            event.key === 'ArrowLeft' ||
            event.key === 'ArrowRight' ||
            event.key === 'PageUp' ||
            event.key === 'PageDown')
        ) {
          historyManagerRef.current.addState(
            sceneSetupRef.current.scene,
            `After ${transformMode} with keyboard`,
          );
          setCanUndo(historyManagerRef.current.canUndo());
          setCanRedo(historyManagerRef.current.canRedo());
        }
      }
    },
    [
      selectedObjects,
      transformMode,
      transformSpeed,
      modelRef,
      sceneSetupRef,
      historyManagerRef,
      setSelectedObjects,
      setTransformMode,
      setTransformSpeed,
      setCanUndo,
      setCanRedo,
    ],
  );

  // Add event listeners for selection and keyboard controls
  useEffect(() => {
    if (!containerRef.current) return;

    const currentContainer = containerRef.current;

    // Track mouse down position to distinguish between click and drag
    const handleMouseDown = (event: MouseEvent) => {
      mouseDownPositionRef.current = { x: event.clientX, y: event.clientY };
      isDraggingRef.current = false;
    };

    // Track mouse move to detect dragging
    const handleMouseMove = (event: MouseEvent) => {
      if (!mouseDownPositionRef.current) return;

      const dx = Math.abs(event.clientX - mouseDownPositionRef.current.x);
      const dy = Math.abs(event.clientY - mouseDownPositionRef.current.y);

      // If the mouse has moved more than a few pixels, consider it a drag
      if (dx > 3 || dy > 3) {
        isDraggingRef.current = true;
      }
    };

    // Handle mouse up to determine if it was a click or a drag
    const handleMouseUp = (event: MouseEvent) => {
      // Only process as a click if we weren't dragging
      if (mouseDownPositionRef.current && !isDraggingRef.current) {
        handleObjectSelection(event);
      }

      // Reset tracking
      mouseDownPositionRef.current = null;
    };

    // Add mouse event listeners
    currentContainer.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);

    // Add keyboard event listener for object transformation
    window.addEventListener('keydown', handleKeyDown);

    return () => {
      // Remove all event listeners
      currentContainer.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleObjectSelection, handleKeyDown, containerRef]);

  // Update selection outlines whenever selectedObjects changes
  useEffect(() => {
    if (selectedObjects.length > 0) {
      updateSelectionOutlines();
    } else {
      clearSelectionOutlines();
    }
  }, [selectedObjects, updateSelectionOutlines, clearSelectionOutlines]);

  // Save initial scene state when model is loaded
  useEffect(() => {
    if (!sceneSetupRef.current || !modelRef.current) return;

    // Clear history when a new model is loaded
    historyManagerRef.current.clear();

    // Save initial state
    historyManagerRef.current.addState(
      sceneSetupRef.current.scene,
      'Initial state',
    );

    // Update undo/redo availability
    setCanUndo(historyManagerRef.current.canUndo());
    setCanRedo(historyManagerRef.current.canRedo());
  }, [
    modelData,
    sceneSetupRef,
    modelRef,
    historyManagerRef,
    setCanUndo,
    setCanRedo,
  ]);

  // No longer need the effect to reposition model when placeOnGrid changes
  // as models are always placed on the grid

  // Return the container div that will hold the Three.js canvas
  return <div className="w-full h-full" ref={containerRef}></div>;
}
