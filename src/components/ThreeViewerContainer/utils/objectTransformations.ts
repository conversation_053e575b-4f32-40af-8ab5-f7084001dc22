import * as THREE from 'three';

export type TransformMode = 'translate' | 'rotate' | 'scale';

// Apply transformation to an object based on keyboard input
export const transformObject = (
  object: THREE.Object3D,
  key: string,
  mode: TransformMode,
  speed: number,
): void => {
  const moveAmount = speed;
  const rotateAmount = (speed * Math.PI) / 8;
  const scaleAmount = speed * 0.1;

  switch (mode) {
    case 'translate':
      if (key === 'ArrowUp') object.position.y += moveAmount;
      if (key === 'ArrowDown') object.position.y -= moveAmount;
      if (key === 'ArrowLeft') object.position.x -= moveAmount;
      if (key === 'ArrowRight') object.position.x += moveAmount;
      if (key === 'PageUp') object.position.z -= moveAmount;
      if (key === 'PageDown') object.position.z += moveAmount;
      break;

    case 'rotate':
      if (key === 'ArrowUp') object.rotation.x += rotateAmount;
      if (key === 'ArrowDown') object.rotation.x -= rotateAmount;
      if (key === 'ArrowLeft') object.rotation.y += rotateAmount;
      if (key === 'ArrowRight') object.rotation.y -= rotateAmount;
      if (key === 'PageUp') object.rotation.z += rotateAmount;
      if (key === 'PageDown') object.rotation.z -= rotateAmount;
      break;

    case 'scale':
      if (key === 'ArrowUp') object.scale.y += scaleAmount;
      if (key === 'ArrowDown') object.scale.y -= scaleAmount;
      if (key === 'ArrowLeft') object.scale.x -= scaleAmount;
      if (key === 'ArrowRight') object.scale.x += scaleAmount;
      if (key === 'PageUp') object.scale.z += scaleAmount;
      if (key === 'PageDown') object.scale.z -= scaleAmount;
      break;
  }
};

// Create a bounding box outline for the selected object
export const createSelectionOutline = (
  object: THREE.Object3D,
): THREE.LineSegments => {
  const box = new THREE.Box3().setFromObject(object);

  if (box.isEmpty()) {
    // Create a small default box if the bounding box is empty
    const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
    // Use a more contrasting color (bright blue)
    const material = new THREE.LineBasicMaterial({
      color: 0x0055ff, // Bright blue color for better contrast against light background
    });
    const wireframe = new THREE.LineSegments(
      new THREE.EdgesGeometry(geometry),
      material,
    );
    wireframe.position.copy(object.position);

    // Make the outline render on top of other objects
    wireframe.renderOrder = 999;
    material.depthTest = false;

    return wireframe;
  }

  // Add padding to make the outline larger than the object
  const padding = 0.05; // 5% padding
  const width = (box.max.x - box.min.x) * (1 + padding);
  const height = (box.max.y - box.min.y) * (1 + padding);
  const depth = (box.max.z - box.min.z) * (1 + padding);

  const geometry = new THREE.BoxGeometry(width, height, depth);

  // Adjust the position to center the outline on the object
  const center = box.getCenter(new THREE.Vector3());

  // Create wireframe material with a more contrasting color
  const material = new THREE.LineBasicMaterial({
    color: 0x0055ff, // Bright blue color for better contrast against light background
  });

  const wireframe = new THREE.LineSegments(
    new THREE.EdgesGeometry(geometry),
    material,
  );

  wireframe.position.copy(center);

  // Make the outline render on top of other objects
  wireframe.renderOrder = 999;
  material.depthTest = false;

  return wireframe;
};
