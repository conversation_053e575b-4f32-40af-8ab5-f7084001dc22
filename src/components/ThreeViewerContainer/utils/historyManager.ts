import * as THREE from 'three';

// Define a type for scene state
export interface SceneState {
  objects: {
    uuid: string;
    position: THREE.Vector3;
    rotation: THREE.Euler;
    scale: THREE.Vector3;
    visible: boolean;
  }[];
  timestamp: number;
  description?: string; // Optional description of the state (e.g., "After AI prompt: 'Place table in corner'")
}

export class HistoryManager {
  private history: SceneState[] = [];
  private currentIndex: number = -1;
  private maxHistorySize: number = 50; // Limit history size to prevent memory issues

  // Add a new state to history
  addState(scene: THREE.Scene, description?: string): void {
    // Create a snapshot of the current scene state
    const state: SceneState = {
      objects: [],
      timestamp: Date.now(),
      description,
    };

    // Store position, rotation, scale, and visibility of each object
    scene.traverse(object => {
      // Only store objects that can be manipulated (not cameras, lights, etc.)
      if (object instanceof THREE.Mesh || object instanceof THREE.Group) {
        state.objects.push({
          uuid: object.uuid,
          position: object.position.clone(),
          rotation: object.rotation.clone(),
          scale: object.scale.clone(),
          visible: object.visible,
        });
      }
    });

    // If we're not at the end of history, remove future states
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1);
    }

    // Add the new state
    this.history.push(state);
    this.currentIndex = this.history.length - 1;

    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
      this.currentIndex--;
    }
  }

  // Restore to a previous state
  undo(scene: THREE.Scene): boolean {
    if (this.currentIndex <= 0) {
      return false; // Can't undo if at the beginning of history
    }

    this.currentIndex--;
    this.applyState(scene, this.history[this.currentIndex]);
    return true;
  }

  // Restore to a future state (after undo)
  redo(scene: THREE.Scene): boolean {
    if (this.currentIndex >= this.history.length - 1) {
      return false; // Can't redo if at the end of history
    }

    this.currentIndex++;
    this.applyState(scene, this.history[this.currentIndex]);
    return true;
  }

  // Apply a state to the scene
  private applyState(scene: THREE.Scene, state: SceneState): void {
    // Map of UUIDs to stored object states
    const stateMap = new Map(state.objects.map(obj => [obj.uuid, obj]));

    // Apply stored state to matching objects in the scene
    scene.traverse(object => {
      const storedState = stateMap.get(object.uuid);
      if (storedState) {
        object.position.copy(storedState.position);
        object.rotation.copy(storedState.rotation);
        object.scale.copy(storedState.scale);
        object.visible = storedState.visible;
      }
    });
  }

  // Check if undo is available
  canUndo(): boolean {
    return this.currentIndex > 0;
  }

  // Check if redo is available
  canRedo(): boolean {
    return this.currentIndex < this.history.length - 1;
  }

  // Get the current state description
  getCurrentStateDescription(): string | undefined {
    if (this.currentIndex >= 0 && this.currentIndex < this.history.length) {
      return this.history[this.currentIndex].description;
    }
    return undefined;
  }

  // Get all history entries
  getHistory(): { index: number; description?: string; timestamp: number }[] {
    return this.history.map((state, index) => ({
      index,
      description: state.description,
      timestamp: state.timestamp,
    }));
  }

  // Clear history
  clear(): void {
    this.history = [];
    this.currentIndex = -1;
  }
}

export default HistoryManager;
