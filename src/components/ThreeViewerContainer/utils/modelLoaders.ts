import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js';

// Helper functions to load different 3D formats
export const loadGLTF = (url: string): Promise<THREE.Object3D> => {
  return new Promise((resolve, reject) => {
    const loader = new GLTFLoader();
    loader.load(
      url,
      gltf => resolve(gltf.scene),
      undefined,
      error => reject(error),
    );
  });
};

export const loadOBJ = (url: string): Promise<THREE.Object3D> => {
  return new Promise((resolve, reject) => {
    const loader = new OBJLoader();
    loader.load(
      url,
      obj => resolve(obj),
      undefined,
      error => reject(error),
    );
  });
};

export const loadFBX = (url: string): Promise<THREE.Object3D> => {
  return new Promise((resolve, reject) => {
    const loader = new FBXLoader();
    loader.load(
      url,
      fbx => resolve(fbx),
      undefined,
      error => reject(error),
    );
  });
};

export const loadSTL = (url: string): Promise<THREE.Object3D> => {
  return new Promise((resolve, reject) => {
    const loader = new STLLoader();
    loader.load(
      url,
      geometry => {
        // Compute vertex normals if they don't exist
        if (!geometry.hasAttribute('normal')) {
          geometry.computeVertexNormals();
        }

        // Create a better material for STL models
        const material = new THREE.MeshStandardMaterial({
          color: 0x7c9cb0,
          roughness: 0.45,
          metalness: 0.5,
          flatShading: false,
        });

        const mesh = new THREE.Mesh(geometry, material);
        mesh.castShadow = true;
        mesh.receiveShadow = true;

        // Create a group to hold the mesh (for consistency with other loaders)
        const group = new THREE.Group();
        group.add(mesh);

        resolve(group);
      },
      undefined,
      error => reject(error),
    );
  });
};

// Load model based on file type
export const loadModel = async (
  url: string,
  fileType: string,
): Promise<THREE.Object3D> => {
  switch (fileType.toLowerCase()) {
    case 'gltf':
    case 'glb':
      return await loadGLTF(url);
    case 'obj':
      return await loadOBJ(url);
    case 'fbx':
      return await loadFBX(url);
    case 'stl':
      return await loadSTL(url);
    default:
      throw new Error(`Unsupported file format: ${fileType}`);
  }
};

// Process model after loading (center, scale, etc.)
export const processModel = (
  object: THREE.Object3D,
  targetSize: number = 5,
): THREE.Box3 => {
  // Make sure model has materials that can receive shadows
  object.traverse(child => {
    if (child instanceof THREE.Mesh) {
      child.castShadow = true;
      child.receiveShadow = true;

      // If material doesn't exist or is a basic material, create a standard material
      if (
        !child.material ||
        child.material instanceof THREE.MeshBasicMaterial
      ) {
        child.material = new THREE.MeshStandardMaterial({
          color: 0xcccccc,
          roughness: 0.7,
          metalness: 0.2,
        });
      }
    }
  });

  // Center and scale the model
  const box = new THREE.Box3().setFromObject(object);

  // Check if the bounding box is valid
  if (box.isEmpty()) {
    console.warn('Model bounding box is empty, using default positioning');
    object.position.set(0, 0, 0);
    return box;
  }

  const center = box.getCenter(new THREE.Vector3());
  const size = box.getSize(new THREE.Vector3());

  // Calculate the model's dimensions
  const maxDim = Math.max(size.x, size.y, size.z);

  // Apply scaling to normalize the model size
  if (maxDim > 0) {
    const scale = targetSize / maxDim;
    object.scale.set(scale, scale, scale);

    // Recompute bounding box after scaling
    box.setFromObject(object);
    box.getCenter(center);
    box.getSize(size);
  }

  // Calculate the bottom point of the model
  const bottomY = box.min.y;

  // Reset position to center the model horizontally and place it on the grid if enabled
  object.position.x = -center.x;
  object.position.z = -center.z;

  // if (placeOnGrid) {
  // Place the model on top of the grid by setting Y position to make the bottom touch the grid
  object.position.y = -bottomY;
  // } else {
  //   // Center the model vertically
  //   console.log("Centering model vertically, setting y to:", -center.y);
  //   object.position.y = -center.y;
  // }

  return box;
};
