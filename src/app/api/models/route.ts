import { NextRequest, NextResponse } from 'next/server';
import { ModelService } from '@/lib/modelService';

// GET /api/models - получить все модели
export async function GET() {
  try {
    const models = await ModelService.getAllModels();
    return NextResponse.json(models);
  } catch (error) {
    console.error('Error fetching models:', error);
    return NextResponse.json(
      { error: 'Failed to fetch models' },
      { status: 500 }
    );
  }
}

// POST /api/models - создать или получить модель
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { fileName, filePath, fileSize } = body;

    if (!fileName) {
      return NextResponse.json(
        { error: 'fileName is required' },
        { status: 400 }
      );
    }

    const model = await ModelService.getOrCreateModel({
      fileName,
      filePath,
      fileSize
    });

    return NextResponse.json(model);
  } catch (error) {
    console.error('Error creating/getting model:', error);
    return NextResponse.json(
      { error: 'Failed to create/get model' },
      { status: 500 }
    );
  }
}
