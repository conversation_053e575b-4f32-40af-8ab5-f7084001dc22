import { NextRequest, NextResponse } from 'next/server';
import { ModelService } from '@/lib/modelService';

// GET /api/models/[fileName]/state - получить состояние модели
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ fileName: string }> },
) {
  try {
    const { fileName } = await params;
    const decodedFileName = decodeURIComponent(fileName);

    const model = await ModelService.getModelByFileName(decodedFileName);
    if (!model) {
      return NextResponse.json({ error: 'Model not found' }, { status: 404 });
    }

    const state = await ModelService.getModelState(model.id);
    return NextResponse.json(state);
  } catch (error) {
    console.error('Error fetching model state:', error);
    return NextResponse.json(
      { error: 'Failed to fetch model state' },
      { status: 500 },
    );
  }
}

// POST /api/models/[fileName]/state - сохранить состояние модели
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ fileName: string }> },
) {
  try {
    const { fileName } = await params;
    const decodedFileName = decodeURIComponent(fileName);
    const body = await request.json();
    const { objectStates } = body;

    if (!Array.isArray(objectStates)) {
      return NextResponse.json(
        { error: 'objectStates must be an array' },
        { status: 400 },
      );
    }

    const model = await ModelService.getModelByFileName(decodedFileName);
    if (!model) {
      return NextResponse.json({ error: 'Model not found' }, { status: 404 });
    }

    await ModelService.saveModelState(model.id, objectStates);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving model state:', error);
    return NextResponse.json(
      { error: 'Failed to save model state' },
      { status: 500 },
    );
  }
}

// PUT /api/models/[fileName]/state - обновить состояние конкретного объекта
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ fileName: string }> },
) {
  try {
    const { fileName } = await params;
    const decodedFileName = decodeURIComponent(fileName);
    const body = await request.json();
    const objectState = body;

    if (!objectState.objectId) {
      return NextResponse.json(
        { error: 'objectId is required' },
        { status: 400 },
      );
    }

    const model = await ModelService.getModelByFileName(decodedFileName);
    if (!model) {
      return NextResponse.json({ error: 'Model not found' }, { status: 404 });
    }

    const updatedState = await ModelService.updateObjectState(
      model.id,
      objectState,
    );
    return NextResponse.json({
      objectId: updatedState.objectId,
      objectName: updatedState.objectName,
      position: JSON.parse(updatedState.position),
      rotation: JSON.parse(updatedState.rotation),
      scale: JSON.parse(updatedState.scale),
      visible: updatedState.visible,
      selected: updatedState.selected,
      properties: updatedState.properties
        ? JSON.parse(updatedState.properties)
        : null,
    });
  } catch (error) {
    console.error('Error updating object state:', error);
    return NextResponse.json(
      { error: 'Failed to update object state' },
      { status: 500 },
    );
  }
}
