import { NextRequest, NextResponse } from 'next/server';
import { ModelService } from '@/lib/modelService';

// GET /api/models/[fileName] - получить модель по имени файла
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ fileName: string }> },
) {
  try {
    const { fileName } = await params;
    const decodedFileName = decodeURIComponent(fileName);
    const model = await ModelService.getModelByFileName(decodedFileName);

    if (!model) {
      return NextResponse.json({ error: 'Model not found' }, { status: 404 });
    }

    return NextResponse.json(model);
  } catch (error) {
    console.error('Error fetching model:', error);
    return NextResponse.json(
      { error: 'Failed to fetch model' },
      { status: 500 },
    );
  }
}

// DELETE /api/models/[fileName] - удалить модель
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ fileName: string }> },
) {
  try {
    const { fileName } = await params;
    const decodedFileName = decodeURIComponent(fileName);
    const model = await ModelService.getModelByFileName(decodedFileName);

    if (!model) {
      return NextResponse.json({ error: 'Model not found' }, { status: 404 });
    }

    await ModelService.deleteModel(model.id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting model:', error);
    return NextResponse.json(
      { error: 'Failed to delete model' },
      { status: 500 },
    );
  }
}
