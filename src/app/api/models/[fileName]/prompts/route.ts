import { NextRequest, NextResponse } from 'next/server';
import { ModelService } from '@/lib/modelService';

// GET /api/models/[fileName]/prompts - получить историю промптов
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ fileName: string }> },
) {
  try {
    const { fileName } = await params;
    const decodedFileName = decodeURIComponent(fileName);
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');

    const model = await ModelService.getModelByFileName(decodedFileName);
    if (!model) {
      return NextResponse.json({ error: 'Model not found' }, { status: 404 });
    }

    const prompts = await ModelService.getPromptHistory(model.id, limit);

    return NextResponse.json(
      prompts.map(prompt => ({
        ...prompt,
        metadata: prompt.metadata ? JSON.parse(prompt.metadata) : null,
      })),
    );
  } catch (error) {
    console.error('Error fetching prompts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch prompts' },
      { status: 500 },
    );
  }
}

// POST /api/models/[fileName]/prompts - создать новый промпт
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ fileName: string }> },
) {
  try {
    const { fileName } = await params;
    const decodedFileName = decodeURIComponent(fileName);
    const body = await request.json();
    const { userPrompt, promptType, metadata } = body;

    if (!userPrompt || !promptType) {
      return NextResponse.json(
        { error: 'userPrompt and promptType are required' },
        { status: 400 },
      );
    }

    const model = await ModelService.getModelByFileName(decodedFileName);
    if (!model) {
      return NextResponse.json({ error: 'Model not found' }, { status: 404 });
    }

    const prompt = await ModelService.savePrompt(model.id, {
      userPrompt,
      promptType,
      metadata,
    });

    return NextResponse.json({
      ...prompt,
      metadata: prompt.metadata ? JSON.parse(prompt.metadata) : null,
    });
  } catch (error) {
    console.error('Error creating prompt:', error);
    return NextResponse.json(
      { error: 'Failed to create prompt' },
      { status: 500 },
    );
  }
}
