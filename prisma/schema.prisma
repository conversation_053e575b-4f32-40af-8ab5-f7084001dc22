// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Модель для хранения информации о загруженных 3D моделях
model Model {
  id          String   @id @default(cuid())
  fileName    String   @unique // Название файла модели (ключ для мэтчинга)
  filePath    String?  // Путь к файлу (если нужно)
  fileSize    Int?     // Размер файла в байтах
  uploadedAt  DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Связи
  analysis    ModelAnalysis?
  prompts     Prompt[]
  modelStates ModelState[]

  @@map("models")
}

// Модель для хранения результатов анализа моделей
model ModelAnalysis {
  id          String   @id @default(cuid())
  modelId     String
  analysisType String  // Тип анализа (например, "ai_analysis", "geometry_analysis")
  result      String   // JSON строка с результатами анализа
  metadata    String?  // Дополнительные метаданные в JSON
  createdAt   DateTime @default(now())

  // Связи
  model       Model    @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@map("model_analyses")
}

// Модель для хранения истории промптов пользователя
model Prompt {
  id          String   @id @default(cuid())
  modelId     String
  userPrompt  String   // Промпт пользователя
  aiResponse  String?  // Ответ AI
  promptType  String   // Тип промпта (например, "analysis", "modification", "question")
  status      String   @default("pending") // pending, completed, failed
  metadata    String?  // Дополнительные данные в JSON
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Связи
  model       Model    @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@map("prompts")
}

// Модель для хранения текущего состояния объектов в модели
model ModelState {
  id          String   @id @default(cuid())
  modelId     String
  objectId    String   // ID объекта в 3D сцене
  objectName  String?  // Название объекта
  position    String   // JSON с координатами позиции {x, y, z}
  rotation    String   // JSON с углами поворота {x, y, z}
  scale       String   // JSON с масштабом {x, y, z}
  visible     Boolean  @default(true)
  selected    Boolean  @default(false)
  properties  String?  // Дополнительные свойства объекта в JSON
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Связи
  model       Model    @relation(fields: [modelId], references: [id], onDelete: Cascade)

  @@unique([modelId, objectId])
  @@map("model_states")
}
