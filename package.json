{"name": "audes", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\""}, "dependencies": {"@prisma/client": "^6.8.2", "@types/three": "^0.176.0", "next": "15.3.1", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "three": "^0.176.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}