This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

### Setting up the Claude API

This application uses Claude AI for 3D scene manipulation. To use this feature:

1. Sign up for an account at [Anthropic](https://console.anthropic.com/)
2. Get your API key from the Anthropic Console
3. Copy the `.env.example` file to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```
4. Add your Claude API key to the `.env.local` file:
   ```
   PUBLIC_CLAUDE_API_KEY=your_claude_api_key_here
   ```

### Running the Development Server

Run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## AI-Powered 3D Scene Manipulation

This application includes an AI-powered feature that allows you to manipulate 3D objects using natural language commands. The AI service uses Claude API to:

1. Analyze the 3D scene and identify objects
2. Interpret natural language commands from the user
3. Generate precise transformations for objects in the scene
4. Apply those transformations to the objects

Example commands you can try:

- "Move the table to the left corner"
- "Rotate the chair to face the center"
- "Make the lamp twice as tall"
- "Place the sofa against the back wall"

If you don't have a Claude API key, the application will fall back to a simple mock implementation with limited functionality.

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
